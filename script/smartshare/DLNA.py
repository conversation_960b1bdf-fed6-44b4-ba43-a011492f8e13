# -*- coding:utf-8 -*-
import time
from subprocess import PIPE, STDOUT, Popen
from uiautomator2 import Device
from lxml import etree
import unittest
import configparser
import re
from datetime import datetime

def timeout_command(command):
    """
    python2 版popen没有timeout
    注意：adb的命令不一定带关键字 shell
    如过需要打印返回值到log中，output传入参数为True
    """
    try:
        state=Popen(command, shell=True, stdout=PIPE, stderr=STDOUT)
        state.wait()
        out = state.stdout.read().decode().strip()
    except Exception as e:
        return ""
    return out

def get_device_id(id):
    cmd = 'adb -s {} shell getprop ro.product.name'.format(id)
    device_id = timeout_command(cmd)
    return device_id

class DLNAshare(unittest.TestCase):

    def __init__(self, phone_id=None, tv_id=None):
        """
        初始化DLNA投屏处理器
        :param phone_id: 手机设备ID，如果为None则从配置文件读取
        :param tv_id: 电视设备ID，如果为None则从配置文件读取
        """
        super().__init__()  # 调用父类构造函数

        if phone_id and tv_id:
            # 直接使用传入的设备ID
            self.phone_id = phone_id
            self.tv_id = tv_id
        else:
            # 从配置文件读取
            self.phone_id, self.tv_id = self.read_config()

        self.d_phone = None
        self.setup_phone_connection()

    def setup_phone_connection(self):
        """设置手机连接"""
        try:
            self.remove_uiautomator()      # 先停止手机上原有的uiautomator，否则容易报错
            self.d_phone = Device(self.phone_id)
            print(self.d_phone.info)
            print(f"手机连接成功: {self.phone_id}")
        except Exception as e:
            print(f"手机连接失败: {e}")

    def setUp(self):
        """保持兼容性的setUp方法"""
        if not self.d_phone:
            self.setup_phone_connection()

    def tearDown(self):
        pass

    def remove_uiautomator(self):  #
        """重新初始化手机端的uiautomator2"""
        Popen('adb connect %s' % self.phone_id, shell=True,stdout=PIPE, stderr=STDOUT).wait()
        time.sleep(3)
        Popen('adb -s %s shell /data/local/tmp/atx-agent server --stop' % self.phone_id, shell=True,stdout=PIPE, stderr=STDOUT).wait()
        Popen('adb -s %s shell rm /data/local/tmp/atx-agent' % self.phone_id, shell=True,stdout=PIPE, stderr=STDOUT).wait()
        Popen('adb -s %s shell pm uninstall com.github.uiautomator' % self.phone_id, shell=True,stdout=PIPE, stderr=STDOUT).wait()
        Popen('adb -s %s shell pm uninstall com.github.uiautomator.test' % self.phone_id, shell=True,stdout=PIPE, stderr=STDOUT).wait()
        Popen('adb -s %s root' % self.phone_id, shell=True,stdout=PIPE, stderr=STDOUT).wait()

    def read_config(self):
        cf = configparser.ConfigParser()
        cf.read("script/smartshare/config.ini", encoding="utf-8")
        try:
            phone_id = cf.get('devices_info', 'phone_id')
            tv_id = cf.get('devices_info', 'tv_id')
            print("phone id:{}".format(phone_id))
            print("tv id:{}".format(tv_id))
            return phone_id, tv_id
        except Exception as e:
            print("script/smartshare/config.ini does not exists")
            print(e)

    """"手机DLNA投屏"""
    def smartshare_phone2tv(self):
        for retry in range(4):
            # print('第 {} 次运行手机端脚本'.format(retry+1))
            if retry < 3:
                Popen('adb -s %s shell input keyevent HOME' % self.phone_id, shell=True,
                      stdout=PIPE, stderr=STDOUT).wait()
                Popen("adb -s %s shell am force-stop com.tencent.qqlive" % self.phone_id, shell=True,
                      stdout=PIPE, stderr=STDOUT).wait()
                print("打开腾讯视频")
                Popen("adb -s %s shell am start -S com.tencent.qqlive/.ona.activity.SplashHomeActivity" % self.phone_id,
                      shell=True,
                      stdout=PIPE, stderr=STDOUT).wait()
                time.sleep(10)
                # self.d_phone(text="跳过").click()
                # time.sleep(10)
                # todo 处理弹窗
                # self.d_phone.disable_popups()
                if self.d_phone(text='我知道了').exists:
                    self.d_phone(text='我知道了').click()
                # self.d_phone(resourceId="com.tencent.qqlive:id/arg", text="首页").wait.exists()
                # 判断是否进入腾讯视频
                state = Popen("adb -s %s shell dumpsys activity | grep ResumedAc" % self.phone_id, shell=True,
                              stdout=PIPE,
                              stderr=STDOUT)
                state.wait()
                out = state.stdout.read().strip()
                if b'com.tencent.qqlive/.ona.activity.SplashHomeActivity' in out:
                    print('成功进入腾讯视频')
                    time.sleep(3)
                print("进入个人中心")
                try:
                    assert self.d_phone(resourceId="",text="个人中心").exists, "fail to find Personal Center"
                    self.d_phone(resourceId="", text="个人中心").click()
                except Exception as e:
                    print(e)
                time.sleep(3)
                print("进入观看历史界面")
                try:
                    assert self.d_phone(resourceId="",text="观看历史").exists, "fail to find Watch history"
                    self.d_phone(resourceId="", text="观看历史").click()
                except Exception as e:
                    print(e)
                time.sleep(3)
                print("播放历史视频")
                try:
                    assert self.d_phone(resourceId="",text="长相思 第二季").exists, "fail to find the TV series"
                    self.d_phone(resourceId="", text="长相思 第二季").click()
                except Exception as e:
                    print(e)
                time.sleep(10)
                # logger.info("点击播放一个视频")
                # print("播放电视剧页面下视频")
                # assert self.d_phone(resourceId="",text="电视剧").exists, "fail to find"
                # try:
                #     assert self.d_phone(resourceId="",text="电视剧").exists, "fail to find dianshiju"
                #     self.d_phone(resourceId="", text="电视剧").click()
                # except Exception as e:
                #     print(e)
                # time.sleep(10)
                # self.d_phone.xpath('//*[@content-desc="全部剧集"]/android.view.ViewGroup[2]').click()
                # self.d_phone.xpath('//androidx.viewpager.widget.ViewPager/android.widget.RelativeLayout[1]/android.view.ViewGroup[1]/android.widget.FrameLayout[1]/android.view.ViewGroup[1]/androidx.recyclerview.widget.RecyclerView[1]/android.widget.FrameLayout[2]/android.view.ViewGroup[1]/android.view.ViewGroup[1]').click()
                # self.d_phone(text="全部剧集").click()
                # self.d_phone(className='android.view.ViewGroup', descriptionStartsWith=',').click()
                # icon = self.d_phone(resourceId="com.tencent.qqlive:id/arg").child(
                #     className="android.widget.FrameLayout", index=3)
                # time.sleep(3)
                # if self.d_phone(resourceId="com.tencent.qqlive:id/arg", text="投屏").exists:
                #     print("toupingcg")
                # print("等待广告120s")
                # time.sleep(120)
                print("手机端点击小电视图标进入投屏界面")
                # self.d_phone(resourceId="com.tencent.qqlive:id/arg", text="投屏")
                # time.sleep(10)
                if self.d_phone(resourceId="com.tencent.qqlive:id/arg", text="投屏").exists:
                    self.d_phone(resourceId="com.tencent.qqlive:id/arg", text="投屏").click()  # 点击小电视图标
                else:
                    print("手机端触发投屏图标失败")
                time.sleep(10)
                # todo 选择投屏设备 需要手动填坐标
                # element = self.d_phone.xpath(
                #     '//*[@resource-id="android:id/content"]/android.widget.FrameLayout[3]/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.ViewGroup[1]/android.view.ViewGroup[4]/android.view.ViewGroup[1]/android.view.ViewGroup[2]/android.view.ViewGroup[1]/android.widget.LinearLayout[1]/android.widget.ImageView[1]')
                # element.click()
                # Popen("adb -s %s shell input tap 207 1383" % self.phone_id, shell=True, stdout=PIPE, stderr=STDOUT)
                # time.sleep(5)
                # todo 判断手机投屏
                if self.d_phone(resourceId="com.tencent.qqlive:id/arg", text="音量").exists:
                    print("手机端投屏成功")
                    break
                else:
                    print("手机可能未进入投屏界面或反应慢")
                    print("重新开始手机投屏")
            else:
                print('三次运行手机端脚本失败')
                out = timeout_command("adb -s {} shell dumpsys activity | grep ResumedAc".format(self.phone_id))
                # self.assertFalse(out, msg="{'case_result':'ERROR','comment':'手机端发起投屏失败'}")


    def check_tv(self):
        """判断电视投屏接收"""
        time.sleep(10)  # miplay投屏接收时间长 10s
        state = Popen("adb -s %s shell dumpsys activity | grep ResumedAc" % self.tv_id, shell=True, stdout=PIPE,
                      stderr=STDOUT)
        state.wait()
        out = state.stdout.read().strip()
        # tv_name = get_device_id(self.tv_id)
        # if tv_name == "finch":
        if b'com.xiaomi.mitv.smartshare/.dlna.dmr.ui.MultiPlayerActivity' in out: # 小米电视
            print("电视端接收投屏成功")
            return True
        elif b'com.xiaomi.mitv.smartshare/com.hpplay.sdk.sink.business.BusinessActivity' in out: # 小米电视
            print("电视端接收投屏成功")
            return True
        else:
            print('电视端接收投屏失败，查看当前 tv activity或投屏协议')  # 手机端投过去了，但电视端没有接收
            # self.assertFalse(out, msg="{'case_result':'FAIL','comment':'电视端接收投屏失败'}")

    def reset_tv(self):
        """投屏断开后电视返回桌面"""
        Popen("adb -s %s shell input keyevent HOME" % self.tv_id, shell=True, stdout=PIPE,
              stderr=STDOUT)  # 暂时不判断电视回没回了
        print("投屏断开后电视返回桌面")


    def testtv_2phonetime_abort(self,timeout=60):
        print("debuging toupingla")
        print("phone tv:{}".format(self.tv_id, self.phone_id))
        for i in range(6):
            print("-time--{}".format(i))
            self.smartshare_phone2tv()
            self.check_tv()
            print('-------------------电视投屏中，计时1min------------------')
            start_time = time.time()
            while time.time() - start_time < timeout:
                pass
            self.reset_tv()
            print("-------------------投屏断开，再次投屏------------------")


    def testtv_2phonetime(self,timeout=60):
        print("phone tv:{}".format(self.tv_id, self.phone_id))
        print('-------------------电视投屏中，计时1min------------------')
        start_time = time.time()
        while time.time() - start_time < timeout:
            pass
        Popen("adb -s %s shell input keyevent HOME" % self.tv_id, shell=True, stdout=PIPE,
              stderr=STDOUT)
        time.sleep(10)
        try:
            assert self.d_phone(resourceId="com.tencent.qqlive:id/arg", text="重新投屏").exists, "fail to find"
            self.d_phone(resourceId="com.tencent.qqlive:id/arg", text="重新投屏").click()
        except Exception as e:
            print(e)
            print("restart")
            self.smartshare_phone2tv()
        self.check_tv()
        time.sleep(30)



    def testtv_2smartshare_tencent(self):
        success_count = 0
        for i in range(20):
            Popen("adb -s %s shell input keyevent HOME" % self.tv_id, shell=True, stdout=PIPE,
                  stderr=STDOUT)
            time.sleep(10)
            try:
                assert self.d_phone(resourceId="com.tencent.qqlive:id/arg", text="重新投屏").exists, "fail to find"
                self.d_phone(resourceId="com.tencent.qqlive:id/arg", text="重新投屏").click()
            except Exception as e:
                print(e)
                print("restart")
                self.smartshare_phone2tv()
            result = self.check_tv()
            if result:
                success_count += 1
            time.sleep(30)
        pass_rate = (success_count / 20) * 100
        print(f"测试次数: 20")
        print(f"成功次数: {success_count}")
        print(f"通过率: {pass_rate:.2f}%")
        self.assertFalse(f"通过率: {pass_rate:.2f}%", msg="{'comment':'手机投屏成功率'}")

    def find_dlna_devices(self, text, tv_name):
        devices = [d.strip() for d in text.split("|") if d.strip()]
        # print(devices)
        target_devices = [device for device in devices if tv_name in device]
        print(target_devices)
        if target_devices:
            return {"status": "found", "devices": target_devices}
        else:
            return {"status": "not_found", "message": "未发现DLNA投屏设备"}


    def calculate_pass_rate(results):
        """
        统计一组测试结果中 status=found 的通过率
        :param results: 包含多个测试结果的列表，每个结果来自 find_dlna_devices()
        :return: 通过率百分比（保留两位小数）
        """

    def collect_test_results(self, input_texts):
        global test_results
        # 清空历史结果（若需要保留历史数据可删除此段）
        test_results = []
        for text in input_texts:
            result = self.find_dlna_devices(text)
            test_results.append({
                "input": text,  # 可选：记录原始输入文本
                "status": result["status"],
                "devices": result.get("devices", []),
                "message": result.get("message", "")
            })
        return test_results

    def test_DLNA_DISCOVERY_FIRST(self):
        Popen("adb -s %s shell input keyevent HOME" % self.phone_id, shell=True, stdout=PIPE, stderr=STDOUT)
        time.sleep(5)
        Popen("adb -s %s shell am force-stop com.xiaomi.mitv.smartshare.dmc" % self.phone_id, shell=True, stdout=PIPE,
              stderr=STDOUT)
        time.sleep(5)
        Popen(
            "adb -s %s shell am start -S com.xiaomi.mitv.smartshare.dmc/com.xiaomi.mitv.smartshare.mediacontrol.MainActivity" % self.phone_id,
            shell=True,
            stdout=PIPE, stderr=STDOUT).wait()
        time.sleep(5)

    def test_DLNA_DISCOVERY_SECOND(self):
        Popen("adb -s %s shell input keyevent HOME" % self.tv_id, shell=True, stdout=PIPE,stderr=STDOUT)
        # 点击搜索调试模式
        try:
            assert self.d_phone(resourceId="com.xiaomi.mitv.smartshare.dmc:id/btnDebug",text="搜索调试模式").exists, "fail to find Search button"
            self.d_phone(resourceId="com.xiaomi.mitv.smartshare.dmc:id/btnDebug",text="搜索调试模式").click()
        except Exception as e:
            print(e)
        time.sleep(15)

        # cmd = "adb -s %s shell cat /storage/emulated/0/Download/deviceList.txt" % self.phone_id
        # test_inputs = timeout_command(cmd)
        # all_results = self.collect_test_results(test_inputs)
        # print(all_results)
        # for i, res in enumerate(all_results, 1):
        #     print(f"测试 {i}:")
        #     print(f"状态: {res['status']}")
        #     if res["status"] == "found":
        #         print(f"匹配设备: {res['devices']}")
        #     else:
        #         print(f"提示信息: {res['message']}")
        #     print("-" * 30)
    def test_DLNA_DISCOVERY_SECOND1(self):
        Popen("adb -s %s shell input keyevent HOME" % self.tv_id, shell=True, stdout=PIPE,stderr=STDOUT)
        Popen("adb -s %s shell input keyevent HOME" % self.phone_id, shell=True, stdout=PIPE, stderr=STDOUT)
        time.sleep(2)
        Popen("adb -s %s shell am force-stop com.xiaomi.cast.dlna.tools" % self.phone_id, shell=True, stdout=PIPE,
              stderr=STDOUT)
        time.sleep(5)
        Popen(
            "adb -s %s shell am start -S com.xiaomi.cast.dlna.tools/com.xiaomi.cast.dlna.tools.MainActivity" % self.phone_id,
            shell=True,
            stdout=PIPE, stderr=STDOUT).wait()
        time.sleep(15)


    def test_run_dlna_discovery(self,tv_name):
        """
        统计一组测试结果中 status=found 的通过率
        :param results: 包含多个测试结果的列表，每个结果来自 find_dlna_devices()
        :return: 通过率百分比（保留两位小数）
        """
        results = []
        success_count = 0
        self.test_DLNA_DISCOVERY_FIRST()
        for i in range(100):
            self.test_DLNA_DISCOVERY_SECOND()
            cmd = "adb -s %s shell cat /storage/emulated/0/Download/deviceList.txt" % self.phone_id
            test_inputs = timeout_command(cmd)
            result = self.find_dlna_devices(test_inputs,tv_name)
            time.sleep(2)
            Popen("adb -s %s shell input keyevent KEYCODE_BACK" % self.phone_id, shell=True, stdout=PIPE,
                  stderr=STDOUT)
            time.sleep(5)
            # all_test_results = results.append(self.find_dlna_devices(test_inputs))  # 存储所有结果
            # print(all_test_results)
            if result.get("status") == "found":
                success_count += 1
        # # 计算成功率
        pass_rate = (success_count / 100) * 100
        # pass_rate = sum(1 for res in results if res["status"] == "found") / len(results) * 100
        # 输出结果
        print(f"测试次数: 100")
        print(f"成功次数: {success_count}")
        print(f"通过率: {pass_rate:.2f}%")
        self.assertFalse(f"通过率: {pass_rate:.2f}%", msg="{'comment':'手机投屏发现率'}")

    def smartshare_tool2tv(self,tv_name):
        for retry in range(4):
            # print('第 {} 次运行手机端脚本'.format(retry+1))
            if retry < 3:
                Popen('adb -s %s shell input keyevent HOME' % self.phone_id, shell=True,
                      stdout=PIPE, stderr=STDOUT).wait()
                Popen("adb -s %s shell am force-stop com.xiaomi.mitv.smartshare.dmc" % self.phone_id, shell=True,
                      stdout=PIPE, stderr=STDOUT).wait()
                print("打开DLNA投屏工具")
                Popen(
                    "adb -s %s shell am start -S com.xiaomi.mitv.smartshare.dmc/com.xiaomi.mitv.smartshare.mediacontrol.MainActivity" % self.phone_id,
                    shell=True,
                    stdout=PIPE, stderr=STDOUT).wait()
                time.sleep(5)
                print("点击搜索投屏设备")
                try:
                    assert self.d_phone(resourceId="com.xiaomi.mitv.smartshare.dmc:id/btnSearch",
                                        text="搜索投屏设备").exists, "fail to find Search button"
                    self.d_phone(resourceId="com.xiaomi.mitv.smartshare.dmc:id/btnSearch", text="搜索投屏设备").click()
                except Exception as e:
                    print(e)
                time.sleep(10)
                print("点击搜索到的设备")
                try:
                    assert self.d_phone(resourceId="com.xiaomi.mitv.smartshare.dmc:id/textLine1",
                                        text=tv_name).exists, "fail to find TV name"
                    self.d_phone(resourceId="com.xiaomi.mitv.smartshare.dmc:id/textLine1", text=tv_name).click()
                except Exception as e:
                    print(e)
                time.sleep(5)
                # 判断手机投屏
                if self.d_phone(resourceId="com.xiaomi.mitv.smartshare.dmc:id/btn_stop").exists:
                    print("手机端投屏成功")
                    break
                else:
                    print("手机可能未进入投屏界面或反应慢")
                    print("重新开始手机投屏")
            else:
                print('三次运行手机端脚本失败')

    def check_tool2tv(self):
        """判断电视投屏接收"""
        time.sleep(5)
        state = Popen("adb -s %s shell dumpsys activity | grep ResumedAc" % self.tv_id, shell=True, stdout=PIPE,
                      stderr=STDOUT)
        state.wait()
        out = state.stdout.read().strip()
        if b'com.xiaomi.mitv.smartshare/.dlna.dmr.ui.MultiPlayerActivity' in out: # 小米电视
            print("电视端接收投屏成功")
            return True
        elif b'com.huawei.mediacenter/.videoplayer.InternalVideoPlayer' in out:  # 华为智慧屏
            print("电视端接收投屏成功")
            return True
        elif b'com.tcl.MultiScreenInteraction_TV/com.tcl.allcast.presentation.activity.PresentationActivity' in out:  # TCL
            print("电视端接收投屏成功")
            return True
        elif b'com.ms.ucast.ucastapp/.common.FusionCastActivity' in out:  # Hisense
            print("电视端接收投屏成功")
            return True
        else:
            print('电视端接收投屏失败，查看当前 tv activity或投屏协议')

    def test_dlna_tool2tv(self,tv_name):
        success_count = 0
        Popen("adb -s %s shell am force-stop com.xiaomi.mitv.smartshare.dmc" % self.phone_id, shell=True,
              stdout=PIPE, stderr=STDOUT).wait()
        for i in range(100):
            print(f"第{i+1}次投屏")
            Popen("adb -s %s shell input keyevent HOME" % self.tv_id, shell=True, stdout=PIPE,
                  stderr=STDOUT)
            time.sleep(5)
            try:
                assert self.d_phone(resourceId="com.xiaomi.mitv.smartshare.dmc:id/textLine1", text=tv_name).exists, "fail to find"
                time.sleep(3)
                self.d_phone(resourceId="com.xiaomi.mitv.smartshare.dmc:id/textLine1", text=tv_name).click()
                # time.sleep(5)
                # if self.d_phone(resourceId="com.xiaomi.mitv.smartshare.dmc:id/btn_stop").exists:
                #     print("手机端投屏成功")
                # else:
                #     self.smartshare_tool2tv()
            except Exception as e:
                print(e)
                print("restart")
                self.smartshare_tool2tv(tv_name)
            result = self.check_tool2tv()
            if result:
                success_count += 1
            else:
                self.smartshare_tool2tv(tv_name)
            time.sleep(5)
        pass_rate = (success_count / 100) * 100
        print(f"测试次数: 100")
        print(f"成功次数: {success_count}")
        print(f"通过率: {pass_rate:.2f}%")
        self.assertFalse(f"通过率: {pass_rate:.2f}%", msg="{'comment':'手机投屏成功率'}")

    def smartshare_openDmc(self,tv_name):
        MAX_RETRY = 1

        for retry in range(MAX_RETRY):
        
            print("尝试重新打开手机端DLNA投屏工具")

            # 先回到手机主界面，强制停止DMC
            Popen(f'adb -s {self.phone_id} shell input keyevent HOME', shell=True, stdout=PIPE, stderr=STDOUT).wait()
            Popen(f'adb -s {self.phone_id} shell am force-stop com.xiaomi.mitv.smartshare.dmc', shell=True, stdout=PIPE, stderr=STDOUT).wait()

            print("启动DMC主Activity")
            Popen(f'adb -s {self.phone_id} shell am start -S com.xiaomi.mitv.smartshare.dmc/com.xiaomi.mitv.smartshare.mediacontrol.MainActivity',
                  shell=True, stdout=PIPE, stderr=STDOUT).wait()

            time.sleep(3)

            print("尝试点击“搜索投屏设备”按钮开始搜索")
            try:
                search_btn = self.d_phone(resourceId="com.xiaomi.mitv.smartshare.dmc:id/btnSearch", text="搜索投屏设备")
                assert search_btn.exists, "未找到“搜索投屏设备”按钮"
                search_btn.click()
                time.sleep(5)
            except Exception as e:
                print(f"点击搜索按钮异常: {e}")

            return True

        # 尝试了MAX_RETRY次仍未成功
        print(f"{MAX_RETRY} 次尝试均失败，停止尝试")
        return False  # 失败返回


    def smartshare_checkTVCastResult(self):
        """判断电视投屏接收"""
        time.sleep(5)
        state = Popen("adb -s %s shell dumpsys activity | grep ResumedAc" % self.tv_id, shell=True, stdout=PIPE,
                      stderr=STDOUT)
        state.wait()
        out = state.stdout.read().strip()
        if b'com.xiaomi.mitv.smartshare/.dlna.dmr.ui.MultiPlayerActivity' in out: # 小米电视
            print("电视端接收投屏成功")
            return True
        elif b'com.xiaomi.mitv.smartshare/com.hpplay.sdk.sink.business.BusinessActivity' in out:  # 华为智慧屏
            print("电视端接收投屏成功")
            return True
        elif b'com.huawei.mediacenter/.videoplayer.InternalVideoPlayer' in out:  # 华为智慧屏
            print("电视端接收投屏成功")
            return True
        elif b'com.tcl.MultiScreenInteraction_TV/com.tcl.allcast.presentation.activity.PresentationActivity' in out:  # TCL
            print("电视端接收投屏成功")
            return True
        elif b'com.ms.ucast.ucastapp/.common.FusionCastActivity' in out:  # Hisense
            print("电视端接收投屏成功")
            return True
        else:
            print("电视端接收投屏失败，查看当前 tv activity或投屏协议")

    def test_xiaomi_dlna_tool2tv(self,tv_name):
        success_count = 0
        test_count = 100
        current_try = 0  # 当前已完成有效尝试次数
        attempt = 0  # 当前总共尝试了几次（包括失败重试）
        failure_cnt = 4  # 尝试次数太多，则认为当次投屏失败
        warning_cnt = 0

        print(f"\n====== 开始DLNA投屏成功率压力测试，总共 {test_count} 次 ======\n")

        while current_try < test_count:
            attempt += 1
            if (attempt > failure_cnt) :
                # 尝试次数太多，投屏失败
                print("手机端投屏尝试次数太多，手机投屏端一直失败")
                print("电视端投屏失败 ❌")
                print(f"第 {current_try + 1} 次投屏完成，成功次数：{success_count}\n")
                attempt = 0
                current_try += 1
                warning_cnt += 1
                continue;


            print(f"第{current_try + 1}次投屏（尝试次数：{attempt}）")

            # 重置电视环境
            print("重置电视环境")
            Popen(f"adb -s {self.tv_id} shell input keyevent HOME", shell=True, stdout=PIPE, stderr=STDOUT)
            time.sleep(6)

            print("使用手机发端搜索目标电视")
            try:
                assert self.d_phone(resourceId="com.xiaomi.mitv.smartshare.dmc:id/textLine1", text=tv_name).exists, \
                    f"未找到目标设备名称"
                time.sleep(3)
                print("点击搜索到的目标设备进行投屏")
                self.d_phone(resourceId="com.xiaomi.mitv.smartshare.dmc:id/textLine1", text=tv_name).click()

                time.sleep(2)
                # 判断手机是否成功投屏
                if not self.d_phone(resourceId="com.xiaomi.mitv.smartshare.dmc:id/btn_stop").exists:
                    print("手机端投屏失败，重新尝试当前轮次")
                    self.smartshare_openDmc(tv_name)
                    continue  # 不增加 current_try，重试本轮，只需要增加attempt

            except Exception as e:
                print(f"搜索或点击目标电视失败，即将重新打开手机发端: {e}")
                self.smartshare_openDmc(tv_name)
                continue  # 不增加 current_try，重试本轮，只需要增加attempt

            print("手机端投屏成功，检查电视端状态")

            result = self.smartshare_checkTVCastResult()
            if result:
                success_count += 1
                print("电视端投屏成功 ✅")
                attempt = 0;
            else:
                attempt = 0;
                print("电视端投屏失败 ❌")

                # 投屏失败，则需要重新打开手机端发端
                self.smartshare_openDmc(tv_name)

            # 本轮投屏流程完成，增加有效次数
            current_try += 1
            print(f"第 {current_try} 次投屏完成，成功次数：{success_count}\n")

        warning_rate = (warning_cnt / test_count) * 100
        pass_rate = (success_count / test_count) * 100
        print(
            f"\n测试完成：总次数: {test_count}，成功次数: {success_count}，成功率: {pass_rate:.2f}%，异常率: {warning_rate:.2f}%")
        self.assertFalse(f"通过率: {pass_rate:.2f}%", msg="{'comment':'手机投屏成功率'}")

    def test_dlna_tool2tv_xiaomi(self):
        self.test_xiaomi_dlna_tool2tv("xiaomidlna")

    def test_run_dlna_discovery_xiaomi(self):
        self.test_run_dlna_discovery("xiaomidlna")

    def test_dlna_tool2tv_huawei(self):
        self.test_xiaomi_dlna_tool2tv("huaweidlna")

    def test_run_dlna_discovery_huawei(self):
        self.test_run_dlna_discovery("huaweidlna")

    def test_dlna_tool2tv_TCL(self):
        self.test_xiaomi_dlna_tool2tv("tcldlna(10.181.26.175)")

    def test_run_dlna_discovery_TCL(self):
        self.test_run_dlna_discovery("tcldlna")

    def test_dlna_tool2tv_Hisense(self):
        self.test_xiaomi_dlna_tool2tv("Hisdlna")

    def test_run_dlna_discovery_Hisense(self):
        self.test_run_dlna_discovery("Hisdlna")

# if __name__ == '__main__':
#     phone_id = "af915268"
#     tv_id = '10.189.133.218'
#     d_phone = Device(phone_id)
#     a = DLNAshare(phone_id, tv_id, d_phone)
#     a.testtv_2smartshare()

    # a.testtv_2phonetime()



